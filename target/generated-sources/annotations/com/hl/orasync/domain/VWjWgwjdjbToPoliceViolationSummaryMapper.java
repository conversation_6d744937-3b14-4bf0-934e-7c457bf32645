package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceViolationSummary;
import com.hl.archive.domain.entity.PoliceViolationSummaryToVWjWgwjdjbMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__593;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__593.class,
    uses = {ConversionUtils.class,PoliceViolationSummaryToVWjWgwjdjbMapper.class},
    imports = {}
)
public interface VWjWgwjdjbToPoliceViolationSummaryMapper extends BaseMapper<VWjWgwjdjb, PoliceViolationSummary> {
  @Mapping(
      target = "acceptDate",
      source = "slsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "detentionOrg",
      source = "lzscdw"
  )
  @Mapping(
      target = "caseDate",
      source = "lasj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "investigationStart",
      source = "dcsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "caseOrg",
      source = "ladw"
  )
  @Mapping(
      target = "detentionDate",
      source = "lzscsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "investigationResult",
      source = "dcqk"
  )
  @Mapping(
      target = "transferDate",
      source = "yjsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "preliminaryOrg",
      source = "chdw"
  )
  @Mapping(
      target = "clueSource",
      source = "wtxslymc"
  )
  @Mapping(
      target = "violationType",
      source = "wjwflxmc"
  )
  @Mapping(
      target = "violationFact",
      source = "wjwfss"
  )
  @Mapping(
      target = "meetingDate",
      source = "hssj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "preliminaryStart",
      source = "chsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "reportOrg",
      source = "djdwmc"
  )
  @Mapping(
      target = "foundDate",
      source = "fxsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "caseNo",
      source = "ajbh"
  )
  @Mapping(
      target = "foundOrg",
      source = "fxdw"
  )
  @Mapping(
      target = "preliminaryResult",
      source = "chqk"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "meetingOrg",
      source = "hsdw"
  )
  @Mapping(
      target = "meetingResult",
      source = "hsjg"
  )
  @Mapping(
      target = "acceptOrg",
      source = "sldw"
  )
  @Mapping(
      target = "transferOrg",
      source = "yjdw"
  )
  @Mapping(
      target = "clueContent",
      source = "wtxsnr"
  )
  @Mapping(
      target = "investigationOrg",
      source = "dcdw"
  )
  PoliceViolationSummary convert(VWjWgwjdjb source);

  @Mapping(
      target = "acceptDate",
      source = "slsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "detentionOrg",
      source = "lzscdw"
  )
  @Mapping(
      target = "caseDate",
      source = "lasj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "investigationStart",
      source = "dcsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "caseOrg",
      source = "ladw"
  )
  @Mapping(
      target = "detentionDate",
      source = "lzscsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "investigationResult",
      source = "dcqk"
  )
  @Mapping(
      target = "transferDate",
      source = "yjsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "preliminaryOrg",
      source = "chdw"
  )
  @Mapping(
      target = "clueSource",
      source = "wtxslymc"
  )
  @Mapping(
      target = "violationType",
      source = "wjwflxmc"
  )
  @Mapping(
      target = "violationFact",
      source = "wjwfss"
  )
  @Mapping(
      target = "meetingDate",
      source = "hssj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "preliminaryStart",
      source = "chsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "reportOrg",
      source = "djdwmc"
  )
  @Mapping(
      target = "foundDate",
      source = "fxsj",
      qualifiedByName = {"strToLocalDate"}
  )
  @Mapping(
      target = "caseNo",
      source = "ajbh"
  )
  @Mapping(
      target = "foundOrg",
      source = "fxdw"
  )
  @Mapping(
      target = "preliminaryResult",
      source = "chqk"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  @Mapping(
      target = "meetingOrg",
      source = "hsdw"
  )
  @Mapping(
      target = "meetingResult",
      source = "hsjg"
  )
  @Mapping(
      target = "acceptOrg",
      source = "sldw"
  )
  @Mapping(
      target = "transferOrg",
      source = "yjdw"
  )
  @Mapping(
      target = "clueContent",
      source = "wtxsnr"
  )
  @Mapping(
      target = "investigationOrg",
      source = "dcdw"
  )
  PoliceViolationSummary convert(VWjWgwjdjb source, @MappingTarget PoliceViolationSummary target);
}
