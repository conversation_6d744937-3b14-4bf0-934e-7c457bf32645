package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceCapabilityEval;
import com.hl.archive.domain.entity.PoliceCapabilityEvalToVWjNlcpMapper;
import io.github.linpeilie.AutoMapperConfig__593;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__593.class,
    uses = {PoliceCapabilityEvalToVWjNlcpMapper.class},
    imports = {}
)
public interface VWjNlcpToPoliceCapabilityEvalMapper extends BaseMapper<VWjNlcp, PoliceCapabilityEval> {
  @Mapping(
      target = "lcid",
      source = "lcid"
  )
  @Mapping(
      target = "policeNumber",
      source = "jh"
  )
  @Mapping(
      target = "orgName",
      source = "dwmc"
  )
  @Mapping(
      target = "evalStatus",
      source = "fszt"
  )
  @Mapping(
      target = "reviewResult",
      source = "shjgmc"
  )
  @Mapping(
      target = "featureName",
      source = "bqMc"
  )
  @Mapping(
      target = "evalLevel",
      source = "bqzMc"
  )
  @Mapping(
      target = "planName",
      source = "famc"
  )
  @Mapping(
      target = "reviewer",
      source = "shrxm"
  )
  @Mapping(
      target = "position",
      source = "zwmc"
  )
  @Mapping(
      target = "participantName",
      source = "sqrxm"
  )
  @Mapping(
      target = "categoryName",
      source = "dlmc"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  PoliceCapabilityEval convert(VWjNlcp source);

  @Mapping(
      target = "lcid",
      source = "lcid"
  )
  @Mapping(
      target = "policeNumber",
      source = "jh"
  )
  @Mapping(
      target = "orgName",
      source = "dwmc"
  )
  @Mapping(
      target = "evalStatus",
      source = "fszt"
  )
  @Mapping(
      target = "reviewResult",
      source = "shjgmc"
  )
  @Mapping(
      target = "featureName",
      source = "bqMc"
  )
  @Mapping(
      target = "evalLevel",
      source = "bqzMc"
  )
  @Mapping(
      target = "planName",
      source = "famc"
  )
  @Mapping(
      target = "reviewer",
      source = "shrxm"
  )
  @Mapping(
      target = "position",
      source = "zwmc"
  )
  @Mapping(
      target = "participantName",
      source = "sqrxm"
  )
  @Mapping(
      target = "categoryName",
      source = "dlmc"
  )
  @Mapping(
      target = "xxzjbh",
      source = "xxzjbh"
  )
  PoliceCapabilityEval convert(VWjNlcp source, @MappingTarget PoliceCapabilityEval target);
}
