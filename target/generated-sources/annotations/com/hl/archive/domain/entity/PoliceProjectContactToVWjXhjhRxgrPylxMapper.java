package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgrPylx;
import com.hl.orasync.domain.VWjXhjhRxgrPylxToPoliceProjectContactMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__593;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__593.class,
    uses = {ConversionUtils.class,VWjXhjhRxgrPylxToPoliceProjectContactMapper.class},
    imports = {}
)
public interface PoliceProjectContactToVWjXhjhRxgrPylxMapper extends BaseMapper<PoliceProjectContact, VWjXhjhRxgrPylx> {
  @Mapping(
      target = "djsj",
      source = "registerTime"
  )
  @Mapping(
      target = "djrxm",
      source = "registeredBy"
  )
  @Mapping(
      target = "jlXxzjbh",
      source = "xhjhZjbh"
  )
  @Mapping(
      target = "pylxrZw",
      source = "contactPosition"
  )
  @Mapping(
      target = "pypj",
      source = "evaluation"
  )
  @Mapping(
      target = "tzld",
      source = "traits"
  )
  @Mapping(
      target = "pylxrXm",
      source = "contactName"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgrPylx convert(PoliceProjectContact source);

  @Mapping(
      target = "djsj",
      source = "registerTime"
  )
  @Mapping(
      target = "djrxm",
      source = "registeredBy"
  )
  @Mapping(
      target = "jlXxzjbh",
      source = "xhjhZjbh"
  )
  @Mapping(
      target = "pylxrZw",
      source = "contactPosition"
  )
  @Mapping(
      target = "pypj",
      source = "evaluation"
  )
  @Mapping(
      target = "tzld",
      source = "traits"
  )
  @Mapping(
      target = "pylxrXm",
      source = "contactName"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgrPylx convert(PoliceProjectContact source, @MappingTarget VWjXhjhRxgrPylx target);
}
