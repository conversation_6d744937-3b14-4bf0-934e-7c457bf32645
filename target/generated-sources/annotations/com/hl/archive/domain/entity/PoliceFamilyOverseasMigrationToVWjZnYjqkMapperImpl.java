package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnYjqk;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-25T14:24:59+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyOverseasMigrationToVWjZnYjqkMapperImpl implements PoliceFamilyOverseasMigrationToVWjZnYjqkMapper {

    @Override
    public VWjZnYjqk convert(PoliceFamilyOverseasMigration source) {
        if ( source == null ) {
            return null;
        }

        VWjZnYjqk vWjZnYjqk = new VWjZnYjqk();

        if ( source.getBasisDate() != null ) {
            vWjZnYjqk.setYjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getBasisDate() ) );
        }
        vWjZnYjqk.setXmPozn( source.getFamilyMemberName() );
        vWjZnYjqk.setYjlx( source.getMigrationCategory() );
        vWjZnYjqk.setXjzcs( source.getCurrentCity() );
        vWjZnYjqk.setGmsfhm( source.getIdCard() );
        vWjZnYjqk.setBz( source.getRemarks() );
        vWjZnYjqk.setYjgj( source.getMigrationCountry() );
        vWjZnYjqk.setYjzjhm( source.getMigrationDocumentNumber() );

        return vWjZnYjqk;
    }

    @Override
    public VWjZnYjqk convert(PoliceFamilyOverseasMigration source, VWjZnYjqk target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getBasisDate() != null ) {
            target.setYjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getBasisDate() ) );
        }
        else {
            target.setYjsj( null );
        }
        target.setXmPozn( source.getFamilyMemberName() );
        target.setYjlx( source.getMigrationCategory() );
        target.setXjzcs( source.getCurrentCity() );
        target.setGmsfhm( source.getIdCard() );
        target.setBz( source.getRemarks() );
        target.setYjgj( source.getMigrationCountry() );
        target.setYjzjhm( source.getMigrationDocumentNumber() );

        return target;
    }
}
