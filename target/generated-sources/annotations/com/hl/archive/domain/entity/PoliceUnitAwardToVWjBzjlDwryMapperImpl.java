package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBzjlDwry;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-25T14:24:59+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceUnitAwardToVWjBzjlDwryMapperImpl implements PoliceUnitAwardToVWjBzjlDwryMapper {

    @Override
    public VWjBzjlDwry convert(PoliceUnitAward source) {
        if ( source == null ) {
            return null;
        }

        VWjBzjlDwry vWjBzjlDwry = new VWjBzjlDwry();

        vWjBzjlDwry.setJljgmc( source.getAwardOrgan() );
        if ( source.getAwardTime() != null ) {
            vWjBzjlDwry.setBzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getAwardTime() ) );
        }
        vWjBzjlDwry.setXm( source.getSupervisorName() );
        vWjBzjlDwry.setDwmc( source.getUnit() );
        vWjBzjlDwry.setBzwh( source.getDocumentNumber() );
        vWjBzjlDwry.setJlmc( source.getAwardName() );
        vWjBzjlDwry.setJh( source.getSupervisorCode() );
        vWjBzjlDwry.setXxzjbh( source.getZjbh() );

        return vWjBzjlDwry;
    }

    @Override
    public VWjBzjlDwry convert(PoliceUnitAward source, VWjBzjlDwry target) {
        if ( source == null ) {
            return target;
        }

        target.setJljgmc( source.getAwardOrgan() );
        if ( source.getAwardTime() != null ) {
            target.setBzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getAwardTime() ) );
        }
        else {
            target.setBzsj( null );
        }
        target.setXm( source.getSupervisorName() );
        target.setDwmc( source.getUnit() );
        target.setBzwh( source.getDocumentNumber() );
        target.setJlmc( source.getAwardName() );
        target.setJh( source.getSupervisorCode() );
        target.setXxzjbh( source.getZjbh() );

        return target;
    }
}
