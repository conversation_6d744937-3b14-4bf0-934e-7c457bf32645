package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjxxx;
import com.hl.orasync.domain.VWjRyjxxxToPoliceRankInfoMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__593;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__593.class,
    uses = {ConversionUtils.class,VWjRyjxxxToPoliceRankInfoMapper.class},
    imports = {}
)
public interface PoliceRankInfoToVWjRyjxxxMapper extends BaseMapper<PoliceRankInfo, VWjRyjxxx> {
  @Mapping(
      target = "xcqsrq",
      source = "rankStartDate"
  )
  @Mapping(
      target = "sxzl",
      source = "rankType"
  )
  @Mapping(
      target = "sxyy",
      source = "promotionReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "sxsj",
      source = "promotionDate"
  )
  @Mapping(
      target = "xc",
      source = "rankTitle"
  )
  @Mapping(
      target = "xczzrq",
      source = "rankEndDate"
  )
  @Mapping(
      target = "sxsxzzj",
      source = "adminLevelAtPromotion"
  )
  VWjRyjxxx convert(PoliceRankInfo source);

  @Mapping(
      target = "xcqsrq",
      source = "rankStartDate"
  )
  @Mapping(
      target = "sxzl",
      source = "rankType"
  )
  @Mapping(
      target = "sxyy",
      source = "promotionReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "sxsj",
      source = "promotionDate"
  )
  @Mapping(
      target = "xc",
      source = "rankTitle"
  )
  @Mapping(
      target = "xczzrq",
      source = "rankEndDate"
  )
  @Mapping(
      target = "sxsxzzj",
      source = "adminLevelAtPromotion"
  )
  VWjRyjxxx convert(PoliceRankInfo source, @MappingTarget VWjRyjxxx target);
}
