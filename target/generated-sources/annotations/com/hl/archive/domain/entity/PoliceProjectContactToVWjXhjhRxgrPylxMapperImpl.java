package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgrPylx;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-25T14:24:59+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceProjectContactToVWjXhjhRxgrPylxMapperImpl implements PoliceProjectContactToVWjXhjhRxgrPylxMapper {

    @Override
    public VWjXhjhRxgrPylx convert(PoliceProjectContact source) {
        if ( source == null ) {
            return null;
        }

        VWjXhjhRxgrPylx vWjXhjhRxgrPylx = new VWjXhjhRxgrPylx();

        if ( source.getRegisterTime() != null ) {
            vWjXhjhRxgrPylx.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegisterTime() ) );
        }
        vWjXhjhRxgrPylx.setDjrxm( source.getRegisteredBy() );
        vWjXhjhRxgrPylx.setJlXxzjbh( source.getXhjhZjbh() );
        vWjXhjhRxgrPylx.setPylxrZw( source.getContactPosition() );
        vWjXhjhRxgrPylx.setPypj( source.getEvaluation() );
        vWjXhjhRxgrPylx.setTzld( source.getTraits() );
        vWjXhjhRxgrPylx.setPylxrXm( source.getContactName() );
        vWjXhjhRxgrPylx.setXxzjbh( source.getZjbh() );

        return vWjXhjhRxgrPylx;
    }

    @Override
    public VWjXhjhRxgrPylx convert(PoliceProjectContact source, VWjXhjhRxgrPylx target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getRegisterTime() != null ) {
            target.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegisterTime() ) );
        }
        else {
            target.setDjsj( null );
        }
        target.setDjrxm( source.getRegisteredBy() );
        target.setJlXxzjbh( source.getXhjhZjbh() );
        target.setPylxrZw( source.getContactPosition() );
        target.setPypj( source.getEvaluation() );
        target.setTzld( source.getTraits() );
        target.setPylxrXm( source.getContactName() );
        target.setXxzjbh( source.getZjbh() );

        return target;
    }
}
