package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrPthz;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-25T14:24:59+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PolicePassportToVWjBrPthzMapperImpl implements PolicePassportToVWjBrPthzMapper {

    @Override
    public VWjBrPthz convert(PolicePassport source) {
        if ( source == null ) {
            return null;
        }

        VWjBrPthz vWjBrPthz = new VWjBrPthz();

        vWjBrPthz.setBgjgmc( source.getCustodyOrganization() );
        if ( source.getIssueDate() != null ) {
            vWjBrPthz.setQfrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getIssueDate() ) );
        }
        if ( source.getExpiryDate() != null ) {
            vWjBrPthz.setYxqz( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getExpiryDate() ) );
        }
        vWjBrPthz.setGmsfhm( source.getIdCard() );
        vWjBrPthz.setHzhm( source.getPassportNumber() );
        vWjBrPthz.setBz( source.getRemarks() );

        return vWjBrPthz;
    }

    @Override
    public VWjBrPthz convert(PolicePassport source, VWjBrPthz target) {
        if ( source == null ) {
            return target;
        }

        target.setBgjgmc( source.getCustodyOrganization() );
        if ( source.getIssueDate() != null ) {
            target.setQfrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getIssueDate() ) );
        }
        else {
            target.setQfrq( null );
        }
        if ( source.getExpiryDate() != null ) {
            target.setYxqz( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getExpiryDate() ) );
        }
        else {
            target.setYxqz( null );
        }
        target.setGmsfhm( source.getIdCard() );
        target.setHzhm( source.getPassportNumber() );
        target.setBz( source.getRemarks() );

        return target;
    }
}
