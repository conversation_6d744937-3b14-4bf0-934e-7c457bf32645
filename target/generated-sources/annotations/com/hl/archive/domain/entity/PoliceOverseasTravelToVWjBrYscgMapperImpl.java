package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrYscg;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-25T14:24:59+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceOverseasTravelToVWjBrYscgMapperImpl implements PoliceOverseasTravelToVWjBrYscgMapper {

    @Override
    public VWjBrYscg convert(PoliceOverseasTravel source) {
        if ( source == null ) {
            return null;
        }

        VWjBrYscg vWjBrYscg = new VWjBrYscg();

        vWjBrYscg.setSdgj( source.getDestinationCountry() );
        if ( source.getEndDate() != null ) {
            vWjBrYscg.setJssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndDate() ) );
        }
        vWjBrYscg.setSy( source.getTravelReason() );
        vWjBrYscg.setGmsfhm( source.getIdCard() );
        vWjBrYscg.setHzhm( source.getPassportNumber() );
        vWjBrYscg.setSpjgmc( source.getApprovalAuthority() );
        if ( source.getStartDate() != null ) {
            vWjBrYscg.setKssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartDate() ) );
        }

        return vWjBrYscg;
    }

    @Override
    public VWjBrYscg convert(PoliceOverseasTravel source, VWjBrYscg target) {
        if ( source == null ) {
            return target;
        }

        target.setSdgj( source.getDestinationCountry() );
        if ( source.getEndDate() != null ) {
            target.setJssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndDate() ) );
        }
        else {
            target.setJssj( null );
        }
        target.setSy( source.getTravelReason() );
        target.setGmsfhm( source.getIdCard() );
        target.setHzhm( source.getPassportNumber() );
        target.setSpjgmc( source.getApprovalAuthority() );
        if ( source.getStartDate() != null ) {
            target.setKssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartDate() ) );
        }
        else {
            target.setKssj( null );
        }

        return target;
    }
}
